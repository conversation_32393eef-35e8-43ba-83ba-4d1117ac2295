/* iOS Components CSS - 兼职达人 App */

/* 基础变量 */
:root {
  --ios-blue: #007AFF;
  --ios-green: #34C759;
  --ios-orange: #FF9500;
  --ios-red: #FF3B30;
  --ios-purple: #AF52DE;
  --ios-pink: #FF2D92;
  --ios-teal: #5AC8FA;
  --ios-yellow: #FFCC00;
  
  --ios-gray: #8E8E93;
  --ios-gray2: #AEAEB2;
  --ios-gray3: #C7C7CC;
  --ios-gray4: #D1D1D6;
  --ios-gray5: #E5E5EA;
  --ios-gray6: #F2F2F7;
  
  --ios-label: #000000;
  --ios-secondary-label: #3C3C43;
  --ios-tertiary-label: #3C3C43;
  --ios-quaternary-label: #3C3C43;
  
  --ios-background: #FFFFFF;
  --ios-secondary-background: #F2F2F7;
  --ios-tertiary-background: #FFFFFF;
  
  --ios-separator: #C6C6C8;
  --ios-opaque-separator: #C6C6C8;
  
  --border-radius-small: 8px;
  --border-radius-medium: 12px;
  --border-radius-large: 16px;
  --border-radius-xl: 20px;
  
  --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--ios-background);
  color: var(--ios-label);
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
}

/* iPhone 15 容器 */
.iphone-container {
  width: 393px;
  height: 852px;
  margin: 20px auto;
  background: #000;
  border-radius: 47px;
  padding: 2px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  position: relative;
}

.iphone-screen {
  width: 100%;
  height: 100%;
  background: var(--ios-background);
  border-radius: 45px;
  overflow: hidden;
  position: relative;
}

/* 状态栏 */
.status-bar {
  height: 54px;
  background: var(--ios-background);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  font-size: 17px;
  font-weight: 600;
  position: relative;
  z-index: 100;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

.battery {
  width: 24px;
  height: 12px;
  border: 1px solid var(--ios-label);
  border-radius: 2px;
  position: relative;
}

.battery::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: var(--ios-label);
  border-radius: 0 1px 1px 0;
}

.battery-fill {
  height: 100%;
  background: var(--ios-green);
  border-radius: 1px;
  width: 80%;
}

/* 导航栏 */
.navbar {
  height: 44px;
  background: var(--ios-background);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 0.5px solid var(--ios-separator);
  position: relative;
}

.navbar-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--ios-label);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.navbar-left, .navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1;
}

.navbar-button {
  color: var(--ios-blue);
  font-size: 17px;
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  margin: -8px;
}

.navbar-button:hover {
  opacity: 0.6;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  background: var(--ios-secondary-background);
  padding-bottom: 34px; /* 为home indicator留空间 */
}

/* 标签栏 */
.tab-bar {
  height: 83px;
  background: var(--ios-background);
  border-top: 0.5px solid var(--ios-separator);
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--ios-gray);
  font-size: 10px;
  padding: 4px 8px;
}

.tab-item.active {
  color: var(--ios-blue);
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

/* Home Indicator */
.home-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 134px;
  height: 5px;
  background: var(--ios-label);
  border-radius: 3px;
  opacity: 0.3;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: var(--border-radius-medium);
  font-size: 17px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
}

.btn-primary {
  background: var(--ios-blue);
  color: white;
}

.btn-primary:hover {
  opacity: 0.8;
}

.btn-secondary {
  background: var(--ios-gray5);
  color: var(--ios-blue);
}

.btn-secondary:hover {
  background: var(--ios-gray4);
}

.btn-success {
  background: var(--ios-green);
  color: white;
}

.btn-danger {
  background: var(--ios-red);
  color: white;
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

.btn-small {
  padding: 8px 16px;
  font-size: 15px;
  min-height: 36px;
}

/* 卡片样式 */
.card {
  background: var(--ios-background);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-small);
  margin: 8px 16px;
  overflow: hidden;
}

.card-header {
  padding: 16px;
  border-bottom: 0.5px solid var(--ios-separator);
}

.card-body {
  padding: 16px;
}

.card-footer {
  padding: 16px;
  border-top: 0.5px solid var(--ios-separator);
  background: var(--ios-gray6);
}

/* 列表样式 */
.list-group {
  background: var(--ios-background);
  border-radius: var(--border-radius-medium);
  margin: 8px 16px;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 0.5px solid var(--ios-separator);
  text-decoration: none;
  color: var(--ios-label);
  min-height: 44px;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background: var(--ios-gray6);
}

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-item-title {
  font-size: 17px;
  font-weight: 400;
  color: var(--ios-label);
}

.list-item-subtitle {
  font-size: 15px;
  color: var(--ios-gray);
}

.list-item-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--ios-blue);
}

.list-item-chevron {
  color: var(--ios-gray2);
  font-size: 14px;
}

/* 输入框样式 */
.form-group {
  margin: 8px 16px;
}

.form-label {
  display: block;
  font-size: 15px;
  color: var(--ios-gray);
  margin-bottom: 8px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--ios-gray4);
  border-radius: var(--border-radius-medium);
  font-size: 17px;
  background: var(--ios-background);
  color: var(--ios-label);
  min-height: 44px;
}

.form-control:focus {
  outline: none;
  border-color: var(--ios-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 搜索框 */
.search-container {
  padding: 8px 16px;
  background: var(--ios-background);
}

.search-box {
  position: relative;
  background: var(--ios-gray6);
  border-radius: var(--border-radius-medium);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-icon {
  color: var(--ios-gray);
  font-size: 16px;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  font-size: 17px;
  color: var(--ios-label);
  outline: none;
}

.search-input::placeholder {
  color: var(--ios-gray);
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }

.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }

.text-primary { color: var(--ios-blue); }
.text-success { color: var(--ios-green); }
.text-danger { color: var(--ios-red); }
.text-warning { color: var(--ios-orange); }
.text-muted { color: var(--ios-gray); }

.bg-primary { background-color: var(--ios-blue); }
.bg-success { background-color: var(--ios-green); }
.bg-danger { background-color: var(--ios-red); }
.bg-warning { background-color: var(--ios-orange); }

.rounded { border-radius: var(--border-radius-medium); }
.rounded-lg { border-radius: var(--border-radius-large); }

.shadow { box-shadow: var(--shadow-medium); }
.shadow-sm { box-shadow: var(--shadow-small); }
.shadow-lg { box-shadow: var(--shadow-large); }
