<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - 职位详情</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .job-header-card {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-large);
            padding: 20px;
            box-shadow: var(--shadow-medium);
        }

        .job-title-main {
            font-size: 24px;
            font-weight: 700;
            color: var(--ios-label);
            margin-bottom: 8px;
        }

        .job-company-main {
            font-size: 16px;
            color: var(--ios-gray);
            margin-bottom: 16px;
        }

        .job-salary-main {
            font-size: 28px;
            font-weight: 800;
            color: var(--ios-orange);
            margin-bottom: 16px;
        }

        .job-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .job-meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--ios-gray);
        }

        .job-meta-icon {
            width: 16px;
            text-align: center;
            color: var(--ios-blue);
        }

        .job-tags-main {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .job-tag-main {
            background: var(--ios-blue);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
        }

        .section-card {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            overflow: hidden;
            box-shadow: var(--shadow-small);
        }

        .section-header-card {
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
            background: var(--ios-gray6);
        }

        .section-title-card {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
        }

        .section-content {
            padding: 16px;
        }

        .description-text {
            line-height: 1.6;
            color: var(--ios-label);
            font-size: 16px;
        }

        .requirements-list {
            list-style: none;
            padding: 0;
        }

        .requirements-list li {
            padding: 8px 0;
            border-bottom: 0.5px solid var(--ios-separator);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .requirements-list li:last-child {
            border-bottom: none;
        }

        .requirement-icon {
            color: var(--ios-green);
            font-size: 14px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .company-logo {
            width: 60px;
            height: 60px;
            background: var(--ios-blue);
            border-radius: var(--border-radius-medium);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .company-details h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .company-details p {
            font-size: 14px;
            color: var(--ios-gray);
            margin-bottom: 2px;
        }

        .action-buttons {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            width: 361px;
            padding: 16px;
            background: var(--ios-background);
            border-top: 0.5px solid var(--ios-separator);
            display: flex;
            gap: 12px;
        }

        .btn-favorite {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--ios-gray6);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ios-gray);
            font-size: 20px;
            cursor: pointer;
        }

        .btn-favorite.active {
            background: var(--ios-red);
            color: white;
        }

        .btn-apply {
            flex: 1;
            height: 50px;
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <button class="navbar-button" onclick="window.history.back()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div class="navbar-title">职位详情</div>
                <div class="navbar-right">
                    <button class="navbar-button">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content" style="padding-bottom: 120px;">
                <!-- 职位基本信息 -->
                <div class="job-header-card">
                    <div class="job-title-main">星巴克咖啡师</div>
                    <div class="job-company-main">星巴克咖啡</div>
                    <div class="job-salary-main">¥25/小时</div>
                    
                    <div class="job-meta">
                        <div class="job-meta-item">
                            <i class="fas fa-map-marker-alt job-meta-icon"></i>
                            <span>朝阳区三里屯</span>
                        </div>
                        <div class="job-meta-item">
                            <i class="fas fa-clock job-meta-icon"></i>
                            <span>周末班</span>
                        </div>
                        <div class="job-meta-item">
                            <i class="fas fa-users job-meta-icon"></i>
                            <span>招聘5人</span>
                        </div>
                        <div class="job-meta-item">
                            <i class="fas fa-calendar job-meta-icon"></i>
                            <span>长期有效</span>
                        </div>
                    </div>

                    <div class="job-tags-main">
                        <span class="job-tag-main">包餐</span>
                        <span class="job-tag-main">培训</span>
                        <span class="job-tag-main">日结</span>
                        <span class="job-tag-main">交通便利</span>
                    </div>
                </div>

                <!-- 职位描述 -->
                <div class="section-card">
                    <div class="section-header-card">
                        <div class="section-title-card">职位描述</div>
                    </div>
                    <div class="section-content">
                        <div class="description-text">
                            我们正在寻找热情、友好的咖啡师加入我们的团队。您将负责为顾客制作高质量的咖啡饮品，提供优质的客户服务，并维护店铺的整洁环境。
                            <br><br>
                            工作内容包括：制作各类咖啡饮品、接待顾客、收银结账、清洁维护等。我们提供完整的培训计划，即使没有经验也可以快速上手。
                        </div>
                    </div>
                </div>

                <!-- 任职要求 -->
                <div class="section-card">
                    <div class="section-header-card">
                        <div class="section-title-card">任职要求</div>
                    </div>
                    <div class="section-content">
                        <ul class="requirements-list">
                            <li>
                                <i class="fas fa-check requirement-icon"></i>
                                <span>年龄18-35岁，身体健康</span>
                            </li>
                            <li>
                                <i class="fas fa-check requirement-icon"></i>
                                <span>具备良好的沟通能力和服务意识</span>
                            </li>
                            <li>
                                <i class="fas fa-check requirement-icon"></i>
                                <span>能够承受一定的工作压力</span>
                            </li>
                            <li>
                                <i class="fas fa-check requirement-icon"></i>
                                <span>有咖啡制作经验者优先</span>
                            </li>
                            <li>
                                <i class="fas fa-check requirement-icon"></i>
                                <span>能够适应周末工作时间</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- 企业信息 -->
                <div class="section-card">
                    <div class="section-header-card">
                        <div class="section-title-card">企业信息</div>
                    </div>
                    <div class="section-content">
                        <div class="company-info">
                            <div class="company-logo">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="company-details">
                                <h3>星巴克咖啡</h3>
                                <p>餐饮服务 · 500-1000人</p>
                                <p>朝阳区三里屯太古里</p>
                                <p>⭐ 4.8分 · 已认证企业</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn-favorite" id="favoriteBtn">
                    <i class="far fa-heart"></i>
                </button>
                <button class="btn-apply" onclick="window.location.href='apply-confirm.html'">
                    立即申请
                </button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 收藏按钮功能
            const favoriteBtn = document.getElementById('favoriteBtn');
            favoriteBtn.addEventListener('click', function() {
                this.classList.toggle('active');
                const icon = this.querySelector('i');
                if (this.classList.contains('active')) {
                    icon.className = 'fas fa-heart';
                } else {
                    icon.className = 'far fa-heart';
                }
            });

            // 申请按钮点击效果
            const applyBtn = document.querySelector('.btn-apply');
            applyBtn.addEventListener('click', function(e) {
                e.preventDefault();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    console.log('跳转到申请确认页');
                }, 150);
            });
        });
    </script>
</body>
</html>
