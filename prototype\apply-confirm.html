<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - 申请确认</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .job-summary {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-large);
            padding: 20px;
            box-shadow: var(--shadow-medium);
        }

        .job-title-main {
            font-size: 20px;
            font-weight: 700;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .job-company-main {
            font-size: 14px;
            color: var(--ios-gray);
            margin-bottom: 12px;
        }

        .job-salary-main {
            font-size: 24px;
            font-weight: 800;
            color: var(--ios-orange);
            margin-bottom: 12px;
        }

        .job-meta-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .job-meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: var(--ios-gray);
        }

        .form-section {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            overflow: hidden;
            box-shadow: var(--shadow-small);
        }

        .form-header {
            padding: 16px;
            background: var(--ios-gray6);
            border-bottom: 0.5px solid var(--ios-separator);
        }

        .form-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
        }

        .form-content {
            padding: 16px;
        }

        .form-row {
            margin-bottom: 16px;
        }

        .form-row:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            font-size: 14px;
            color: var(--ios-label);
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--ios-gray4);
            border-radius: var(--border-radius-small);
            font-size: 16px;
            background: var(--ios-background);
            color: var(--ios-label);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
            font-family: inherit;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid var(--ios-gray4);
            border-radius: 4px;
            background: var(--ios-background);
            cursor: pointer;
            position: relative;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .checkbox.checked {
            background: var(--ios-blue);
            border-color: var(--ios-blue);
        }

        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .checkbox-label {
            font-size: 14px;
            color: var(--ios-label);
            line-height: 1.4;
            cursor: pointer;
        }

        .checkbox-label a {
            color: var(--ios-blue);
            text-decoration: none;
        }

        .action-buttons {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            width: 361px;
            padding: 16px;
            background: var(--ios-background);
            border-top: 0.5px solid var(--ios-separator);
            display: flex;
            gap: 12px;
        }

        .btn-cancel {
            flex: 1;
            height: 50px;
            background: var(--ios-gray6);
            color: var(--ios-gray);
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
        }

        .btn-submit {
            flex: 2;
            height: 50px;
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
        }

        .btn-submit:disabled {
            background: var(--ios-gray4);
            color: var(--ios-gray);
            cursor: not-allowed;
        }

        .tips-card {
            background: var(--ios-yellow);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            padding: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .tips-icon {
            color: var(--ios-orange);
            font-size: 20px;
            margin-top: 2px;
        }

        .tips-content {
            flex: 1;
        }

        .tips-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .tips-text {
            font-size: 14px;
            color: var(--ios-label);
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <button class="navbar-button" onclick="window.history.back()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div class="navbar-title">申请职位</div>
                <div class="navbar-right">
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content" style="padding-bottom: 120px;">
                <!-- 职位摘要 -->
                <div class="job-summary">
                    <div class="job-title-main">星巴克咖啡师</div>
                    <div class="job-company-main">星巴克咖啡</div>
                    <div class="job-salary-main">¥25/小时</div>
                    
                    <div class="job-meta-grid">
                        <div class="job-meta-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>朝阳区三里屯</span>
                        </div>
                        <div class="job-meta-item">
                            <i class="fas fa-clock"></i>
                            <span>周末班</span>
                        </div>
                        <div class="job-meta-item">
                            <i class="fas fa-users"></i>
                            <span>招聘5人</span>
                        </div>
                        <div class="job-meta-item">
                            <i class="fas fa-calendar"></i>
                            <span>长期有效</span>
                        </div>
                    </div>
                </div>

                <!-- 温馨提示 -->
                <div class="tips-card">
                    <div class="tips-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="tips-content">
                        <div class="tips-title">申请提示</div>
                        <div class="tips-text">完善的个人信息和真诚的自我介绍能大大提高申请成功率哦！</div>
                    </div>
                </div>

                <!-- 个人信息 -->
                <div class="form-section">
                    <div class="form-header">
                        <div class="form-title">个人信息</div>
                    </div>
                    <div class="form-content">
                        <div class="form-row">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-input" value="张小明" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-input" value="138****8888" readonly>
                        </div>
                        <div class="form-row">
                            <label class="form-label">可工作时间</label>
                            <select class="form-input form-select" required>
                                <option value="">请选择可工作时间</option>
                                <option value="weekends">仅周末</option>
                                <option value="weekdays">仅工作日</option>
                                <option value="anytime">随时可以</option>
                                <option value="evenings">仅晚上</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 申请信息 -->
                <div class="form-section">
                    <div class="form-header">
                        <div class="form-title">申请信息</div>
                    </div>
                    <div class="form-content">
                        <div class="form-row">
                            <label class="form-label">相关经验</label>
                            <select class="form-input form-select" required>
                                <option value="">请选择相关经验</option>
                                <option value="none">无相关经验</option>
                                <option value="some">有一些经验</option>
                                <option value="experienced">经验丰富</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <label class="form-label">自我介绍</label>
                            <textarea class="form-input form-textarea" placeholder="简单介绍一下自己，包括相关经验、技能特长等..." required></textarea>
                        </div>
                        <div class="form-row">
                            <label class="form-label">期望薪资</label>
                            <select class="form-input form-select">
                                <option value="">接受职位标准薪资</option>
                                <option value="negotiable">面议</option>
                                <option value="higher">希望更高薪资</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 协议确认 -->
                <div class="form-section">
                    <div class="form-content">
                        <div class="checkbox-group">
                            <div class="checkbox" id="agreeCheckbox"></div>
                            <label class="checkbox-label" for="agreeCheckbox">
                                我已阅读并同意<a href="#">《用户协议》</a>和<a href="#">《隐私政策》</a>，确认提供的信息真实有效
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn-cancel" onclick="window.history.back()">
                    取消
                </button>
                <button class="btn-submit" id="submitBtn" disabled>
                    提交申请
                </button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const agreeCheckbox = document.getElementById('agreeCheckbox');
            const submitBtn = document.getElementById('submitBtn');
            const requiredFields = document.querySelectorAll('[required]');

            // 复选框点击事件
            agreeCheckbox.addEventListener('click', function() {
                this.classList.toggle('checked');
                checkFormValidity();
            });

            // 监听必填字段变化
            requiredFields.forEach(field => {
                field.addEventListener('input', checkFormValidity);
                field.addEventListener('change', checkFormValidity);
            });

            function checkFormValidity() {
                const isAgreed = agreeCheckbox.classList.contains('checked');
                const allFieldsFilled = Array.from(requiredFields).every(field => field.value.trim() !== '');
                
                if (isAgreed && allFieldsFilled) {
                    submitBtn.disabled = false;
                } else {
                    submitBtn.disabled = true;
                }
            }

            // 提交申请
            submitBtn.addEventListener('click', function() {
                if (this.disabled) return;
                
                this.style.transform = 'scale(0.95)';
                this.textContent = '提交中...';
                this.disabled = true;
                
                // 模拟提交过程
                setTimeout(() => {
                    alert('申请提交成功！\n\n我们会在24小时内回复您的申请，请保持手机畅通。');
                    window.location.href = 'work.html';
                }, 1500);
            });

            // 按钮点击效果
            const cancelBtn = document.querySelector('.btn-cancel');
            cancelBtn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
