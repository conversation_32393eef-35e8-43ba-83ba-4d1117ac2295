<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - Demo</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <button class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div class="navbar-title">兼职达人</div>
                <div class="navbar-right">
                    <button class="navbar-button">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 这里是页面具体内容 -->
                <div style="padding: 20px; text-align: center;">
                    <h2>兼职达人</h2>
                    <p style="color: var(--ios-gray); margin-top: 10px;">
                        这是基础模板页面
                    </p>
                    
                    <div class="card mt-3">
                        <div class="card-body">
                            <h3>示例卡片</h3>
                            <p style="color: var(--ios-gray); margin-top: 8px;">
                                这是一个示例卡片组件
                            </p>
                        </div>
                    </div>

                    <div class="list-group mt-2">
                        <a href="#" class="list-item">
                            <div class="list-item-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="list-item-content">
                                <div class="list-item-title">示例列表项</div>
                                <div class="list-item-subtitle">这是副标题</div>
                            </div>
                            <div class="list-item-chevron">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </div>

                    <button class="btn btn-primary mt-3" style="width: 100%; max-width: 300px;">
                        示例按钮
                    </button>
                </div>
            </div>

            <!-- 标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item active">
                    <div class="tab-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <span>首页</span>
                </a>
                <a href="work.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <span>我的工作</span>
                </a>
                <a href="profile.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>个人中心</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        // 基础交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 按钮点击效果
            const buttons = document.querySelectorAll('.btn, .navbar-button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // 列表项点击效果
            const listItems = document.querySelectorAll('.list-item');
            listItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'var(--ios-gray6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
