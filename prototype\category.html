<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - 分类浏览</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .category-header {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-large);
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow-medium);
        }

        .category-icon-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--ios-orange);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin: 0 auto 16px;
        }

        .category-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--ios-label);
            margin-bottom: 8px;
        }

        .category-subtitle {
            font-size: 14px;
            color: var(--ios-gray);
            margin-bottom: 16px;
        }

        .category-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: var(--ios-blue);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--ios-gray);
        }

        .filter-tabs {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            padding: 4px;
            display: flex;
            box-shadow: var(--shadow-small);
        }

        .filter-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: var(--border-radius-small);
            font-size: 14px;
            font-weight: 500;
            color: var(--ios-gray);
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .filter-tab.active {
            background: var(--ios-blue);
            color: white;
        }

        .job-card {
            background: var(--ios-background);
            margin: 0 16px 12px;
            border-radius: var(--border-radius-medium);
            padding: 16px;
            box-shadow: var(--shadow-small);
            text-decoration: none;
            color: var(--ios-label);
            display: block;
            transition: transform 0.2s ease;
        }

        .job-card:hover {
            transform: translateY(-1px);
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .job-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .job-company {
            font-size: 14px;
            color: var(--ios-gray);
        }

        .job-salary {
            font-size: 18px;
            font-weight: 700;
            color: var(--ios-orange);
        }

        .job-info {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .job-info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: var(--ios-gray);
        }

        .job-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .job-tag {
            background: var(--ios-gray6);
            color: var(--ios-blue);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .load-more {
            text-align: center;
            padding: 20px;
        }

        .load-more-btn {
            background: var(--ios-gray6);
            color: var(--ios-blue);
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <button class="navbar-button" onclick="window.history.back()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div class="navbar-title">餐饮服务</div>
                <div class="navbar-right">
                    <button class="navbar-button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 分类头部 -->
                <div class="category-header">
                    <div class="category-icon-large">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="category-title">餐饮服务</div>
                    <div class="category-subtitle">为您精选优质餐饮兼职岗位</div>
                    
                    <div class="category-stats">
                        <div class="stat-item">
                            <div class="stat-number">156</div>
                            <div class="stat-label">在招职位</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">¥22</div>
                            <div class="stat-label">平均时薪</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">85%</div>
                            <div class="stat-label">匹配度</div>
                        </div>
                    </div>
                </div>

                <!-- 筛选标签 -->
                <div class="filter-tabs">
                    <a href="#" class="filter-tab active">全部</a>
                    <a href="#" class="filter-tab">服务员</a>
                    <a href="#" class="filter-tab">咖啡师</a>
                    <a href="#" class="filter-tab">收银员</a>
                </div>

                <!-- 职位列表 -->
                <a href="job-detail.html?id=1" class="job-card">
                    <div class="job-header">
                        <div>
                            <div class="job-title">星巴克咖啡师</div>
                            <div class="job-company">星巴克咖啡</div>
                        </div>
                        <div class="job-salary">¥25/时</div>
                    </div>
                    <div class="job-info">
                        <div class="job-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>朝阳区三里屯</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-clock"></i>
                            <span>周末</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-users"></i>
                            <span>5人</span>
                        </div>
                    </div>
                    <div class="job-tags">
                        <span class="job-tag">包餐</span>
                        <span class="job-tag">培训</span>
                        <span class="job-tag">日结</span>
                    </div>
                </a>

                <a href="job-detail.html?id=4" class="job-card">
                    <div class="job-header">
                        <div>
                            <div class="job-title">麦当劳服务员</div>
                            <div class="job-company">麦当劳</div>
                        </div>
                        <div class="job-salary">¥18/时</div>
                    </div>
                    <div class="job-info">
                        <div class="job-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>海淀区中关村</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-clock"></i>
                            <span>全天</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-users"></i>
                            <span>10人</span>
                        </div>
                    </div>
                    <div class="job-tags">
                        <span class="job-tag">包餐</span>
                        <span class="job-tag">培训</span>
                        <span class="job-tag">周结</span>
                    </div>
                </a>

                <a href="job-detail.html?id=5" class="job-card">
                    <div class="job-header">
                        <div>
                            <div class="job-title">海底捞服务员</div>
                            <div class="job-company">海底捞火锅</div>
                        </div>
                        <div class="job-salary">¥22/时</div>
                    </div>
                    <div class="job-info">
                        <div class="job-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>西城区西单</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-clock"></i>
                            <span>晚班</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-users"></i>
                            <span>8人</span>
                        </div>
                    </div>
                    <div class="job-tags">
                        <span class="job-tag">包餐</span>
                        <span class="job-tag">小费</span>
                        <span class="job-tag">日结</span>
                    </div>
                </a>

                <a href="job-detail.html?id=6" class="job-card">
                    <div class="job-header">
                        <div>
                            <div class="job-title">肯德基收银员</div>
                            <div class="job-company">肯德基</div>
                        </div>
                        <div class="job-salary">¥20/时</div>
                    </div>
                    <div class="job-info">
                        <div class="job-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>东城区王府井</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-clock"></i>
                            <span>工作日</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-users"></i>
                            <span>3人</span>
                        </div>
                    </div>
                    <div class="job-tags">
                        <span class="job-tag">培训</span>
                        <span class="job-tag">周结</span>
                        <span class="job-tag">交通便利</span>
                    </div>
                </a>

                <!-- 加载更多 -->
                <div class="load-more">
                    <button class="load-more-btn">加载更多职位</button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选标签切换
            const filterTabs = document.querySelectorAll('.filter-tab');
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 这里可以添加筛选逻辑
                    console.log('筛选:', this.textContent);
                });
            });

            // 职位卡片点击效果
            const jobCards = document.querySelectorAll('.job-card');
            jobCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'translateY(-1px) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-1px)';
                        console.log('跳转到职位详情:', this.getAttribute('href'));
                    }, 150);
                });
            });

            // 加载更多按钮
            const loadMoreBtn = document.querySelector('.load-more-btn');
            loadMoreBtn.addEventListener('click', function() {
                this.textContent = '加载中...';
                this.disabled = true;
                
                // 模拟加载
                setTimeout(() => {
                    this.textContent = '加载更多职位';
                    this.disabled = false;
                    console.log('加载更多职位');
                }, 1000);
            });
        });
    </script>
</body>
</html>
