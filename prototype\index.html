<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - 首页</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .hero-banner {
            background: linear-gradient(135deg, var(--ios-blue) 0%, var(--ios-purple) 100%);
            color: white;
            padding: 24px 16px;
            margin: 0 16px 16px;
            border-radius: var(--border-radius-large);
            text-align: center;
        }

        .hero-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .hero-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            padding: 0 16px;
            margin-bottom: 16px;
        }

        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--ios-label);
            padding: 16px 8px;
            background: var(--ios-background);
            border-radius: var(--border-radius-medium);
            box-shadow: var(--shadow-small);
            transition: transform 0.2s ease;
        }

        .category-item:hover {
            transform: translateY(-2px);
        }

        .category-icon {
            width: 40px;
            height: 40px;
            background: var(--ios-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: 8px;
        }

        .category-name {
            font-size: 12px;
            text-align: center;
            color: var(--ios-label);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            margin-bottom: 8px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--ios-label);
        }

        .section-more {
            color: var(--ios-blue);
            font-size: 16px;
            text-decoration: none;
        }

        .job-card {
            background: var(--ios-background);
            border-radius: var(--border-radius-medium);
            margin: 0 16px 12px;
            padding: 16px;
            box-shadow: var(--shadow-small);
            text-decoration: none;
            color: var(--ios-label);
            display: block;
            transition: transform 0.2s ease;
        }

        .job-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .job-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .job-company {
            font-size: 14px;
            color: var(--ios-gray);
        }

        .job-salary {
            font-size: 18px;
            font-weight: 700;
            color: var(--ios-orange);
        }

        .job-info {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
        }

        .job-info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: var(--ios-gray);
        }

        .job-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .job-tag {
            background: var(--ios-gray6);
            color: var(--ios-blue);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .floating-search {
            position: fixed;
            top: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 361px;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <button class="navbar-button">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                </div>
                <div class="navbar-title">兼职达人</div>
                <div class="navbar-right">
                    <button class="navbar-button" onclick="window.location.href='search.html'">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 搜索框 -->
                <div class="search-container">
                    <div class="search-box" onclick="window.location.href='search.html'">
                        <div class="search-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <input type="text" class="search-input" placeholder="搜索兼职职位..." readonly>
                    </div>
                </div>

                <!-- 英雄横幅 -->
                <div class="hero-banner">
                    <div class="hero-title">找到理想兼职</div>
                    <div class="hero-subtitle">海量优质岗位等你来</div>
                </div>

                <!-- 分类网格 -->
                <div class="category-grid">
                    <a href="category.html?type=food" class="category-item">
                        <div class="category-icon" style="background: var(--ios-orange);">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="category-name">餐饮服务</div>
                    </a>
                    <a href="category.html?type=retail" class="category-item">
                        <div class="category-icon" style="background: var(--ios-green);">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="category-name">零售导购</div>
                    </a>
                    <a href="category.html?type=promotion" class="category-item">
                        <div class="category-icon" style="background: var(--ios-purple);">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="category-name">活动推广</div>
                    </a>
                    <a href="category.html?type=tutor" class="category-item">
                        <div class="category-icon" style="background: var(--ios-teal);">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="category-name">家教辅导</div>
                    </a>
                    <a href="category.html?type=delivery" class="category-item">
                        <div class="category-icon" style="background: var(--ios-red);">
                            <i class="fas fa-motorcycle"></i>
                        </div>
                        <div class="category-name">配送跑腿</div>
                    </a>
                    <a href="category.html?type=admin" class="category-item">
                        <div class="category-icon" style="background: var(--ios-blue);">
                            <i class="fas fa-laptop"></i>
                        </div>
                        <div class="category-name">文员助理</div>
                    </a>
                    <a href="category.html?type=event" class="category-item">
                        <div class="category-icon" style="background: var(--ios-pink);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="category-name">活动兼职</div>
                    </a>
                    <a href="category.html" class="category-item">
                        <div class="category-icon" style="background: var(--ios-gray);">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                        <div class="category-name">更多</div>
                    </a>
                </div>

                <!-- 推荐职位 -->
                <div class="section-header">
                    <div class="section-title">推荐职位</div>
                    <a href="job-list.html" class="section-more">查看更多</a>
                </div>

                <a href="job-detail.html?id=1" class="job-card">
                    <div class="job-header">
                        <div>
                            <div class="job-title">星巴克咖啡师</div>
                            <div class="job-company">星巴克咖啡</div>
                        </div>
                        <div class="job-salary">¥25/时</div>
                    </div>
                    <div class="job-info">
                        <div class="job-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>朝阳区</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-clock"></i>
                            <span>周末</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-users"></i>
                            <span>5人</span>
                        </div>
                    </div>
                    <div class="job-tags">
                        <span class="job-tag">包餐</span>
                        <span class="job-tag">培训</span>
                        <span class="job-tag">日结</span>
                    </div>
                </a>

                <a href="job-detail.html?id=2" class="job-card">
                    <div class="job-header">
                        <div>
                            <div class="job-title">商场促销员</div>
                            <div class="job-company">优衣库</div>
                        </div>
                        <div class="job-salary">¥20/时</div>
                    </div>
                    <div class="job-info">
                        <div class="job-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>海淀区</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-clock"></i>
                            <span>工作日</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-users"></i>
                            <span>3人</span>
                        </div>
                    </div>
                    <div class="job-tags">
                        <span class="job-tag">形象佳</span>
                        <span class="job-tag">周结</span>
                    </div>
                </a>

                <a href="job-detail.html?id=3" class="job-card">
                    <div class="job-header">
                        <div>
                            <div class="job-title">数学家教老师</div>
                            <div class="job-company">学而思教育</div>
                        </div>
                        <div class="job-salary">¥80/时</div>
                    </div>
                    <div class="job-info">
                        <div class="job-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>西城区</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-clock"></i>
                            <span>晚上</span>
                        </div>
                        <div class="job-info-item">
                            <i class="fas fa-users"></i>
                            <span>1人</span>
                        </div>
                    </div>
                    <div class="job-tags">
                        <span class="job-tag">本科以上</span>
                        <span class="job-tag">经验优先</span>
                        <span class="job-tag">月结</span>
                    </div>
                </a>
            </div>

            <!-- 标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item active">
                    <div class="tab-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <span>首页</span>
                </a>
                <a href="work.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <span>我的工作</span>
                </a>
                <a href="profile.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>个人中心</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === 'index.html') {
                        e.preventDefault();
                        return;
                    }
                });
            });

            // 卡片点击效果
            const jobCards = document.querySelectorAll('.job-card');
            jobCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'translateY(-1px) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-1px)';
                        // 这里可以添加页面跳转逻辑
                        console.log('跳转到职位详情页:', this.getAttribute('href'));
                    }, 150);
                });
            });

            // 分类点击效果
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.transform = 'translateY(-2px) scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-2px)';
                        console.log('跳转到分类页面:', this.getAttribute('href'));
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
