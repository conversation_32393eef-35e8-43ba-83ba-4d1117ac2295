# 兼职达人 iOS App 高保真原型

## 📱 项目概述

这是一个专为中国用户设计的兼职招聘 iOS App 高保真原型，采用 iPhone 15 设计规范，完全符合 iOS 原生设计语言。

## 🎯 设计特点

- **iPhone 15 模拟器**：393x852px 屏幕尺寸
- **iOS 原生设计**：遵循 Apple Human Interface Guidelines
- **清新风格**：采用渐变背景和现代化 UI 设计
- **完整交互**：所有可交互元素都有明确的跳转逻辑
- **响应式动效**：按钮点击、卡片悬停等交互反馈

## 📁 文件结构

```
prototype/
├── README.md                 # 项目说明文档
├── ios-components.css        # iOS 组件样式库
├── demo.html                 # 基础模板页面
├── index.html               # 首页
├── job-detail.html          # 职位详情页
├── work.html                # 我的工作页
├── profile.html             # 个人中心页
├── search.html              # 搜索页面
├── apply-confirm.html       # 申请确认页
└── category.html            # 分类浏览页
```

## 🗺️ 页面导航地图

### 主要页面流程

```
首页 (index.html)
├── 搜索 → 搜索页面 (search.html)
├── 职位卡片 → 职位详情页 (job-detail.html)
│   └── 立即申请 → 申请确认页 (apply-confirm.html)
├── 分类图标 → 分类浏览页 (category.html)
│   └── 职位列表 → 职位详情页
└── 底部导航
    ├── 我的工作 (work.html)
    └── 个人中心 (profile.html)
```

### 页面跳转关系

| 起始页面 | 目标页面 | 触发元素 | 跳转说明 |
|---------|---------|---------|---------|
| index.html | search.html | 搜索框/搜索按钮 | 进入搜索页面 |
| index.html | job-detail.html | 职位卡片 | 查看职位详情 |
| index.html | category.html | 分类图标 | 浏览分类职位 |
| job-detail.html | apply-confirm.html | 立即申请按钮 | 申请职位 |
| apply-confirm.html | work.html | 提交成功 | 返回我的工作 |
| category.html | job-detail.html | 职位卡片 | 查看职位详情 |
| 任意页面 | index.html | 底部导航-首页 | 返回首页 |
| 任意页面 | work.html | 底部导航-我的工作 | 查看工作状态 |
| 任意页面 | profile.html | 底部导航-个人中心 | 个人信息管理 |

## 🎨 设计规范

### 颜色系统
- **主色调**：iOS Blue (#007AFF)
- **辅助色**：Green (#34C759), Orange (#FF9500), Red (#FF3B30)
- **背景色**：White (#FFFFFF), Secondary Background (#F2F2F7)
- **文字色**：Label (#000000), Secondary Label (#3C3C43), Gray (#8E8E93)

### 字体规范
- **系统字体**：-apple-system, BlinkMacSystemFont
- **标题**：17-24px, 字重 600-700
- **正文**：14-16px, 字重 400-500
- **辅助文字**：12-13px, 字重 400

### 间距规范
- **页面边距**：16px
- **组件间距**：8px, 12px, 16px
- **内容间距**：4px, 8px, 12px

### 圆角规范
- **小圆角**：8px
- **中圆角**：12px
- **大圆角**：16px, 20px
- **按钮圆角**：根据高度的 50% 或固定值

## 🔧 技术实现

### 核心技术
- **HTML5**：语义化标签结构
- **CSS3**：Flexbox/Grid 布局，CSS 变量
- **JavaScript**：原生 ES6+ 语法
- **Font Awesome**：图标库
- **响应式设计**：适配不同屏幕尺寸

### 交互特性
- **点击反馈**：按钮缩放动效
- **悬停效果**：卡片阴影变化
- **状态管理**：标签切换、表单验证
- **模拟数据**：本地 JavaScript 数据模拟

## 🚀 使用方法

### 本地预览
1. 下载所有文件到本地目录
2. 使用现代浏览器打开 `index.html`
3. 建议使用 Chrome/Safari 获得最佳体验

### 在线预览
直接在浏览器中打开任意 HTML 文件即可预览

### 开发调试
建议使用浏览器开发者工具的移动设备模拟器：
- 设备：iPhone 15 Pro
- 尺寸：393 x 852
- 像素比：3x

## 📋 功能清单

### ✅ 已实现功能

#### 首页 (index.html)
- [x] 搜索框（跳转到搜索页）
- [x] 分类网格（8个主要分类）
- [x] 推荐职位列表
- [x] 职位卡片交互
- [x] 底部导航栏

#### 职位详情页 (job-detail.html)
- [x] 职位基本信息展示
- [x] 职位描述和要求
- [x] 企业信息
- [x] 收藏功能
- [x] 申请按钮（跳转申请页）

#### 我的工作页 (work.html)
- [x] 工作统计卡片
- [x] 标签切换（进行中/已完成/申请记录/收藏夹）
- [x] 工作卡片列表
- [x] 工作状态管理
- [x] 快捷操作按钮

#### 个人中心页 (profile.html)
- [x] 个人信息头部
- [x] 信用分数展示
- [x] 功能菜单列表
- [x] 个人资料管理入口

#### 搜索页面 (search.html)
- [x] 实时搜索功能
- [x] 热门搜索标签
- [x] 搜索历史记录
- [x] 筛选栏
- [x] 搜索结果展示

#### 申请确认页 (apply-confirm.html)
- [x] 职位信息摘要
- [x] 个人信息表单
- [x] 申请信息填写
- [x] 协议确认
- [x] 表单验证

#### 分类浏览页 (category.html)
- [x] 分类头部信息
- [x] 分类统计数据
- [x] 筛选标签
- [x] 职位列表
- [x] 加载更多功能

### 🔄 交互逻辑

#### 页面跳转
- 所有链接都有明确的 href 属性
- JavaScript 控制页面跳转逻辑
- 支持浏览器前进后退

#### 状态管理
- 标签栏活动状态切换
- 表单字段验证
- 按钮禁用/启用状态

#### 动效反馈
- 按钮点击缩放效果
- 卡片悬停阴影变化
- 加载状态提示

## 🎯 设计亮点

### 1. 原生 iOS 体验
- 完全遵循 iOS 设计规范
- 使用系统字体和标准间距
- 原生控件样式和交互

### 2. 清新现代设计
- 渐变背景增加视觉层次
- 卡片化设计提升内容可读性
- 合理的留白和间距

### 3. 完整用户流程
- 从浏览到申请的完整闭环
- 多种职位发现方式
- 个人工作管理功能

### 4. 细致交互设计
- 所有可点击元素都有反馈
- 表单验证和错误提示
- 加载状态和空状态设计

## 📱 适配说明

### 屏幕适配
- 主要适配 iPhone 15 (393x852px)
- 支持 iPhone 14/13/12 系列
- 响应式设计适配不同尺寸

### 浏览器兼容
- Chrome 90+
- Safari 14+
- Firefox 88+
- Edge 90+

## 🔮 后续扩展

### 可添加页面
- 筛选页面 (filter.html)
- 企业详情页 (company-detail.html)
- 工作详情页 (work-detail.html)
- 签到页面 (checkin.html)
- 个人资料编辑页 (profile-edit.html)
- 设置页面 (settings.html)

### 可增强功能
- 地图定位功能
- 实时聊天功能
- 推送通知
- 数据统计图表
- 更多筛选选项

## 📞 技术支持

如有任何问题或建议，请联系开发团队。

---

**兼职达人 v1.0.0**  
*让兼职更简单，让工作更灵活*
