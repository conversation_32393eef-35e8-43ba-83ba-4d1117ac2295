<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - 我的工作</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .tab-nav {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            padding: 4px;
            display: flex;
            box-shadow: var(--shadow-small);
        }

        .tab-nav-item {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: var(--border-radius-small);
            font-size: 16px;
            font-weight: 500;
            color: var(--ios-gray);
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .tab-nav-item.active {
            background: var(--ios-blue);
            color: white;
        }

        .work-card {
            background: var(--ios-background);
            margin: 0 16px 12px;
            border-radius: var(--border-radius-medium);
            padding: 16px;
            box-shadow: var(--shadow-small);
            text-decoration: none;
            color: var(--ios-label);
            display: block;
            transition: transform 0.2s ease;
        }

        .work-card:hover {
            transform: translateY(-1px);
        }

        .work-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .work-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .work-company {
            font-size: 14px;
            color: var(--ios-gray);
        }

        .work-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-ongoing {
            background: var(--ios-green);
            color: white;
        }

        .status-completed {
            background: var(--ios-gray5);
            color: var(--ios-gray);
        }

        .status-pending {
            background: var(--ios-orange);
            color: white;
        }

        .work-info {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .work-info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: var(--ios-gray);
        }

        .work-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .work-action-btn {
            padding: 8px 16px;
            border-radius: var(--border-radius-small);
            font-size: 14px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: all 0.2s ease;
        }

        .btn-primary-small {
            background: var(--ios-blue);
            color: white;
        }

        .btn-secondary-small {
            background: var(--ios-gray6);
            color: var(--ios-blue);
        }

        .btn-success-small {
            background: var(--ios-green);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--ios-gray);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-subtitle {
            font-size: 14px;
            line-height: 1.4;
        }

        .stats-card {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            padding: 20px;
            box-shadow: var(--shadow-small);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--ios-blue);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                </div>
                <div class="navbar-title">我的工作</div>
                <div class="navbar-right">
                    <button class="navbar-button">
                        <i class="fas fa-calendar-alt"></i>
                    </button>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 统计卡片 -->
                <div class="stats-card">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">2</div>
                            <div class="stat-label">进行中</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">15</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">¥2,580</div>
                            <div class="stat-label">本月收入</div>
                        </div>
                    </div>
                </div>

                <!-- 标签导航 -->
                <div class="tab-nav">
                    <a href="#" class="tab-nav-item active" data-tab="ongoing">进行中</a>
                    <a href="#" class="tab-nav-item" data-tab="completed">已完成</a>
                    <a href="#" class="tab-nav-item" data-tab="applications">申请记录</a>
                    <a href="#" class="tab-nav-item" data-tab="favorites">收藏夹</a>
                </div>

                <!-- 进行中的工作 -->
                <div class="tab-content" id="ongoing">
                    <a href="work-detail.html?id=1" class="work-card">
                        <div class="work-header">
                            <div>
                                <div class="work-title">星巴克咖啡师</div>
                                <div class="work-company">星巴克咖啡</div>
                            </div>
                            <div class="work-status status-ongoing">进行中</div>
                        </div>
                        <div class="work-info">
                            <div class="work-info-item">
                                <i class="fas fa-calendar"></i>
                                <span>今天 09:00-17:00</span>
                            </div>
                            <div class="work-info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>朝阳区三里屯</span>
                            </div>
                        </div>
                        <div class="work-actions">
                            <button class="work-action-btn btn-primary-small" onclick="event.preventDefault(); window.location.href='checkin.html'">
                                签到
                            </button>
                            <button class="work-action-btn btn-secondary-small">
                                联系雇主
                            </button>
                        </div>
                    </a>

                    <a href="work-detail.html?id=2" class="work-card">
                        <div class="work-header">
                            <div>
                                <div class="work-title">商场促销员</div>
                                <div class="work-company">优衣库</div>
                            </div>
                            <div class="work-status status-ongoing">进行中</div>
                        </div>
                        <div class="work-info">
                            <div class="work-info-item">
                                <i class="fas fa-calendar"></i>
                                <span>明天 10:00-18:00</span>
                            </div>
                            <div class="work-info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>海淀区中关村</span>
                            </div>
                        </div>
                        <div class="work-actions">
                            <button class="work-action-btn btn-secondary-small">
                                查看详情
                            </button>
                        </div>
                    </a>
                </div>

                <!-- 已完成的工作 -->
                <div class="tab-content" id="completed" style="display: none;">
                    <a href="work-detail.html?id=3" class="work-card">
                        <div class="work-header">
                            <div>
                                <div class="work-title">数学家教老师</div>
                                <div class="work-company">学而思教育</div>
                            </div>
                            <div class="work-status status-completed">已完成</div>
                        </div>
                        <div class="work-info">
                            <div class="work-info-item">
                                <i class="fas fa-calendar"></i>
                                <span>昨天 19:00-21:00</span>
                            </div>
                            <div class="work-info-item">
                                <i class="fas fa-money-bill"></i>
                                <span>¥160</span>
                            </div>
                        </div>
                        <div class="work-actions">
                            <button class="work-action-btn btn-success-small">
                                已结算
                            </button>
                            <button class="work-action-btn btn-secondary-small">
                                评价
                            </button>
                        </div>
                    </a>
                </div>

                <!-- 申请记录 -->
                <div class="tab-content" id="applications" style="display: none;">
                    <div class="work-card">
                        <div class="work-header">
                            <div>
                                <div class="work-title">餐厅服务员</div>
                                <div class="work-company">海底捞火锅</div>
                            </div>
                            <div class="work-status status-pending">待回复</div>
                        </div>
                        <div class="work-info">
                            <div class="work-info-item">
                                <i class="fas fa-clock"></i>
                                <span>申请时间：2小时前</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收藏夹 -->
                <div class="tab-content" id="favorites" style="display: none;">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="far fa-heart"></i>
                        </div>
                        <div class="empty-title">暂无收藏</div>
                        <div class="empty-subtitle">
                            收藏感兴趣的职位<br>
                            方便随时查看和申请
                        </div>
                    </div>
                </div>
            </div>

            <!-- 标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <span>首页</span>
                </a>
                <a href="work.html" class="tab-item active">
                    <div class="tab-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <span>我的工作</span>
                </a>
                <a href="profile.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>个人中心</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签切换功能
            const tabNavItems = document.querySelectorAll('.tab-nav-item');
            const tabContents = document.querySelectorAll('.tab-content');

            tabNavItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有活动状态
                    tabNavItems.forEach(nav => nav.classList.remove('active'));
                    tabContents.forEach(content => content.style.display = 'none');
                    
                    // 添加当前活动状态
                    this.classList.add('active');
                    const targetTab = this.getAttribute('data-tab');
                    document.getElementById(targetTab).style.display = 'block';
                });
            });

            // 工作卡片点击效果
            const workCards = document.querySelectorAll('.work-card');
            workCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    if (e.target.tagName === 'BUTTON') {
                        return; // 如果点击的是按钮，不处理卡片点击
                    }
                    this.style.transform = 'translateY(-1px) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-1px)';
                    }, 150);
                });
            });

            // 按钮点击效果
            const actionBtns = document.querySelectorAll('.work-action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
