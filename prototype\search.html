<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - 搜索</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .search-header {
            background: var(--ios-background);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 0.5px solid var(--ios-separator);
        }

        .search-input-container {
            flex: 1;
            position: relative;
        }

        .search-input-main {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: none;
            background: var(--ios-gray6);
            border-radius: 20px;
            font-size: 16px;
            color: var(--ios-label);
            outline: none;
        }

        .search-input-main::placeholder {
            color: var(--ios-gray);
        }

        .search-input-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--ios-gray);
            font-size: 16px;
        }

        .search-cancel {
            color: var(--ios-blue);
            font-size: 16px;
            text-decoration: none;
            padding: 8px;
            margin: -8px;
        }

        .filter-bar {
            background: var(--ios-background);
            padding: 12px 16px;
            display: flex;
            gap: 8px;
            overflow-x: auto;
            border-bottom: 0.5px solid var(--ios-separator);
        }

        .filter-item {
            padding: 8px 16px;
            border-radius: 20px;
            background: var(--ios-gray6);
            color: var(--ios-gray);
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .filter-item.active {
            background: var(--ios-blue);
            color: white;
        }

        .hot-searches {
            background: var(--ios-background);
            margin: 16px;
            border-radius: var(--border-radius-medium);
            padding: 20px;
            box-shadow: var(--shadow-small);
        }

        .hot-searches-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 16px;
        }

        .hot-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .hot-tag {
            padding: 8px 16px;
            background: var(--ios-gray6);
            color: var(--ios-label);
            border-radius: 20px;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .hot-tag:hover {
            background: var(--ios-blue);
            color: white;
        }

        .search-history {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            overflow: hidden;
            box-shadow: var(--shadow-small);
        }

        .history-header {
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
        }

        .clear-history {
            color: var(--ios-blue);
            font-size: 14px;
            text-decoration: none;
        }

        .history-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid var(--ios-separator);
            text-decoration: none;
            color: var(--ios-label);
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-item:hover {
            background: var(--ios-gray6);
        }

        .history-icon {
            color: var(--ios-gray);
            margin-right: 12px;
            font-size: 16px;
        }

        .search-results {
            padding: 16px;
        }

        .result-count {
            color: var(--ios-gray);
            font-size: 14px;
            margin-bottom: 16px;
        }

        .job-card {
            background: var(--ios-background);
            border-radius: var(--border-radius-medium);
            margin-bottom: 12px;
            padding: 16px;
            box-shadow: var(--shadow-small);
            text-decoration: none;
            color: var(--ios-label);
            display: block;
            transition: transform 0.2s ease;
        }

        .job-card:hover {
            transform: translateY(-1px);
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .job-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .job-company {
            font-size: 14px;
            color: var(--ios-gray);
        }

        .job-salary {
            font-size: 18px;
            font-weight: 700;
            color: var(--ios-orange);
        }

        .job-info {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
        }

        .job-info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: var(--ios-gray);
        }

        .job-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .job-tag {
            background: var(--ios-gray6);
            color: var(--ios-blue);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--ios-gray);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-subtitle {
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 搜索头部 -->
            <div class="search-header">
                <div class="search-input-container">
                    <input type="text" class="search-input-main" placeholder="搜索兼职职位..." id="searchInput">
                    <div class="search-input-icon">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <a href="index.html" class="search-cancel">取消</a>
            </div>

            <!-- 筛选栏 -->
            <div class="filter-bar">
                <a href="#" class="filter-item active">全部</a>
                <a href="#" class="filter-item">餐饮</a>
                <a href="#" class="filter-item">零售</a>
                <a href="#" class="filter-item">推广</a>
                <a href="#" class="filter-item">家教</a>
                <a href="#" class="filter-item">配送</a>
                <a href="filter.html" class="filter-item">
                    <i class="fas fa-sliders-h"></i> 筛选
                </a>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 默认状态：热门搜索和历史记录 -->
                <div id="defaultContent">
                    <!-- 热门搜索 -->
                    <div class="hot-searches">
                        <div class="hot-searches-title">热门搜索</div>
                        <div class="hot-tags">
                            <a href="#" class="hot-tag" data-keyword="咖啡师">咖啡师</a>
                            <a href="#" class="hot-tag" data-keyword="服务员">服务员</a>
                            <a href="#" class="hot-tag" data-keyword="促销员">促销员</a>
                            <a href="#" class="hot-tag" data-keyword="家教">家教</a>
                            <a href="#" class="hot-tag" data-keyword="配送员">配送员</a>
                            <a href="#" class="hot-tag" data-keyword="文员">文员</a>
                            <a href="#" class="hot-tag" data-keyword="导购">导购</a>
                            <a href="#" class="hot-tag" data-keyword="兼职">兼职</a>
                        </div>
                    </div>

                    <!-- 搜索历史 -->
                    <div class="search-history">
                        <div class="history-header">
                            <div class="history-title">搜索历史</div>
                            <a href="#" class="clear-history">清空</a>
                        </div>
                        <a href="#" class="history-item" data-keyword="星巴克">
                            <div class="history-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <span>星巴克</span>
                        </a>
                        <a href="#" class="history-item" data-keyword="数学家教">
                            <div class="history-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <span>数学家教</span>
                        </a>
                        <a href="#" class="history-item" data-keyword="周末兼职">
                            <div class="history-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <span>周末兼职</span>
                        </a>
                    </div>
                </div>

                <!-- 搜索结果 -->
                <div id="searchResults" style="display: none;">
                    <div class="result-count">找到 <span id="resultCount">0</span> 个相关职位</div>
                    
                    <div id="resultsList">
                        <!-- 搜索结果将在这里动态生成 -->
                    </div>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" style="display: none;">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="empty-title">没有找到相关职位</div>
                        <div class="empty-subtitle">
                            试试其他关键词<br>
                            或调整筛选条件
                        </div>
                    </div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const defaultContent = document.getElementById('defaultContent');
            const searchResults = document.getElementById('searchResults');
            const emptyState = document.getElementById('emptyState');
            const resultCount = document.getElementById('resultCount');
            const resultsList = document.getElementById('resultsList');

            // 模拟搜索数据
            const mockJobs = [
                {
                    id: 1,
                    title: '星巴克咖啡师',
                    company: '星巴克咖啡',
                    salary: '¥25/时',
                    location: '朝阳区',
                    time: '周末',
                    people: '5人',
                    tags: ['包餐', '培训', '日结']
                },
                {
                    id: 2,
                    title: '商场促销员',
                    company: '优衣库',
                    salary: '¥20/时',
                    location: '海淀区',
                    time: '工作日',
                    people: '3人',
                    tags: ['形象佳', '周结']
                },
                {
                    id: 3,
                    title: '数学家教老师',
                    company: '学而思教育',
                    salary: '¥80/时',
                    location: '西城区',
                    time: '晚上',
                    people: '1人',
                    tags: ['本科以上', '经验优先', '月结']
                }
            ];

            // 搜索功能
            function performSearch(keyword) {
                if (!keyword.trim()) {
                    showDefaultContent();
                    return;
                }

                // 模拟搜索
                const results = mockJobs.filter(job => 
                    job.title.includes(keyword) || 
                    job.company.includes(keyword) ||
                    job.tags.some(tag => tag.includes(keyword))
                );

                if (results.length > 0) {
                    showSearchResults(results);
                } else {
                    showEmptyState();
                }
            }

            function showDefaultContent() {
                defaultContent.style.display = 'block';
                searchResults.style.display = 'none';
                emptyState.style.display = 'none';
            }

            function showSearchResults(results) {
                defaultContent.style.display = 'none';
                searchResults.style.display = 'block';
                emptyState.style.display = 'none';
                
                resultCount.textContent = results.length;
                resultsList.innerHTML = results.map(job => `
                    <a href="job-detail.html?id=${job.id}" class="job-card">
                        <div class="job-header">
                            <div>
                                <div class="job-title">${job.title}</div>
                                <div class="job-company">${job.company}</div>
                            </div>
                            <div class="job-salary">${job.salary}</div>
                        </div>
                        <div class="job-info">
                            <div class="job-info-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${job.location}</span>
                            </div>
                            <div class="job-info-item">
                                <i class="fas fa-clock"></i>
                                <span>${job.time}</span>
                            </div>
                            <div class="job-info-item">
                                <i class="fas fa-users"></i>
                                <span>${job.people}</span>
                            </div>
                        </div>
                        <div class="job-tags">
                            ${job.tags.map(tag => `<span class="job-tag">${tag}</span>`).join('')}
                        </div>
                    </a>
                `).join('');
            }

            function showEmptyState() {
                defaultContent.style.display = 'none';
                searchResults.style.display = 'none';
                emptyState.style.display = 'block';
            }

            // 搜索输入事件
            searchInput.addEventListener('input', function() {
                performSearch(this.value);
            });

            // 热门标签点击
            const hotTags = document.querySelectorAll('.hot-tag');
            hotTags.forEach(tag => {
                tag.addEventListener('click', function(e) {
                    e.preventDefault();
                    const keyword = this.getAttribute('data-keyword');
                    searchInput.value = keyword;
                    performSearch(keyword);
                });
            });

            // 历史记录点击
            const historyItems = document.querySelectorAll('.history-item');
            historyItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const keyword = this.getAttribute('data-keyword');
                    searchInput.value = keyword;
                    performSearch(keyword);
                });
            });

            // 筛选项点击
            const filterItems = document.querySelectorAll('.filter-item');
            filterItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    if (this.getAttribute('href') === 'filter.html') {
                        return; // 让筛选页面正常跳转
                    }
                    e.preventDefault();
                    filterItems.forEach(f => f.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 清空历史记录
            const clearHistory = document.querySelector('.clear-history');
            clearHistory.addEventListener('click', function(e) {
                e.preventDefault();
                const historyItems = document.querySelectorAll('.history-item');
                historyItems.forEach(item => item.remove());
            });

            // 自动聚焦搜索框
            searchInput.focus();
        });
    </script>
</body>
</html>
