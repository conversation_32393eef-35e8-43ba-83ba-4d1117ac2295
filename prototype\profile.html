<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼职达人 - 个人中心</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .profile-header {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-large);
            padding: 24px;
            text-align: center;
            box-shadow: var(--shadow-medium);
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--ios-blue) 0%, var(--ios-purple) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin: 0 auto 16px;
            position: relative;
        }

        .profile-avatar::after {
            content: '';
            position: absolute;
            bottom: 4px;
            right: 4px;
            width: 20px;
            height: 20px;
            background: var(--ios-green);
            border-radius: 50%;
            border: 3px solid var(--ios-background);
        }

        .profile-name {
            font-size: 22px;
            font-weight: 700;
            color: var(--ios-label);
            margin-bottom: 4px;
        }

        .profile-title {
            font-size: 14px;
            color: var(--ios-gray);
            margin-bottom: 16px;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: var(--ios-blue);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--ios-gray);
        }

        .menu-section {
            background: var(--ios-background);
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            overflow: hidden;
            box-shadow: var(--shadow-small);
        }

        .menu-header {
            padding: 16px;
            background: var(--ios-gray6);
            border-bottom: 0.5px solid var(--ios-separator);
        }

        .menu-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--ios-label);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
            text-decoration: none;
            color: var(--ios-label);
            transition: background-color 0.2s ease;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: var(--ios-gray6);
        }

        .menu-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--border-radius-small);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }

        .menu-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .menu-item-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--ios-label);
        }

        .menu-item-subtitle {
            font-size: 13px;
            color: var(--ios-gray);
        }

        .menu-chevron {
            color: var(--ios-gray2);
            font-size: 14px;
        }

        .menu-badge {
            background: var(--ios-red);
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: 8px;
        }

        .credit-score {
            background: linear-gradient(135deg, var(--ios-green) 0%, var(--ios-teal) 100%);
            color: white;
            padding: 16px;
            margin: 0 16px 16px;
            border-radius: var(--border-radius-medium);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-medium);
        }

        .credit-info h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .credit-info p {
            font-size: 14px;
            opacity: 0.9;
        }

        .credit-number {
            font-size: 32px;
            font-weight: 800;
        }

        .version-info {
            text-align: center;
            padding: 20px;
            color: var(--ios-gray);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                </div>
                <div class="navbar-title">个人中心</div>
                <div class="navbar-right">
                    <button class="navbar-button">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 个人信息头部 -->
                <div class="profile-header">
                    <div class="profile-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="profile-name">张小明</div>
                    <div class="profile-title">大学生 · 已认证</div>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <div class="stat-number">15</div>
                            <div class="stat-label">完成工作</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">4.9</div>
                            <div class="stat-label">评分</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">出勤率</div>
                        </div>
                    </div>
                </div>

                <!-- 信用分数 -->
                <div class="credit-score">
                    <div class="credit-info">
                        <h3>信用分数</h3>
                        <p>优秀信用，更多机会</p>
                    </div>
                    <div class="credit-number">850</div>
                </div>

                <!-- 个人资料 -->
                <div class="menu-section">
                    <div class="menu-header">
                        <div class="menu-title">个人资料</div>
                    </div>
                    <a href="profile-edit.html" class="menu-item">
                        <div class="menu-icon" style="background: var(--ios-blue);">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-item-title">编辑资料</div>
                            <div class="menu-item-subtitle">完善个人信息，提高匹配度</div>
                        </div>
                        <div class="menu-chevron">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    <a href="skills.html" class="menu-item">
                        <div class="menu-icon" style="background: var(--ios-green);">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-item-title">技能标签</div>
                            <div class="menu-item-subtitle">添加技能，获得更多推荐</div>
                        </div>
                        <div class="menu-chevron">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    <a href="experience.html" class="menu-item">
                        <div class="menu-icon" style="background: var(--ios-purple);">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-item-title">工作经历</div>
                            <div class="menu-item-subtitle">展示工作经验</div>
                        </div>
                        <div class="menu-chevron">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                </div>

                <!-- 其他功能 -->
                <div class="menu-section">
                    <div class="menu-header">
                        <div class="menu-title">其他</div>
                    </div>
                    <a href="feedback.html" class="menu-item">
                        <div class="menu-icon" style="background: var(--ios-orange);">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-item-title">意见反馈</div>
                            <div class="menu-item-subtitle">帮助我们改进产品</div>
                        </div>
                        <div class="menu-chevron">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    <a href="about.html" class="menu-item">
                        <div class="menu-icon" style="background: var(--ios-gray);">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-item-title">关于我们</div>
                            <div class="menu-item-subtitle">了解兼职达人</div>
                        </div>
                        <div class="menu-chevron">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                </div>

                <!-- 版本信息 -->
                <div class="version-info">
                    兼职达人 v1.0.0
                </div>
            </div>

            <!-- 标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <span>首页</span>
                </a>
                <a href="work.html" class="tab-item">
                    <div class="tab-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <span>我的工作</span>
                </a>
                <a href="profile.html" class="tab-item active">
                    <div class="tab-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>个人中心</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 菜单项点击效果
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'var(--ios-gray6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                        console.log('跳转到:', this.getAttribute('href'));
                    }, 200);
                });
            });

            // 信用分数点击效果
            const creditScore = document.querySelector('.credit-score');
            creditScore.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                    console.log('查看信用详情');
                }, 150);
            });
        });
    </script>
</body>
</html>
