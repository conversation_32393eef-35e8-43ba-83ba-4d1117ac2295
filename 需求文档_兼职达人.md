# 兼职达人 iOS App 需求文档

## 应用概述

**中文名称：** 兼职达人  
**英文名称：** PartTime Pro

### 中文商店描述
兼职达人是一款专为求职者打造的兼职招聘平台，汇聚海量优质兼职岗位，涵盖餐饮服务、零售导购、活动推广、家教辅导等多个行业。智能匹配系统根据您的技能和时间安排，精准推荐合适岗位。实时薪资结算，安全可靠的工作环境，让您轻松找到心仪兼职，实现时间与收入的完美平衡。

### 英文商店描述
PartTime Pro is a comprehensive part-time job platform connecting job seekers with quality opportunities across various industries including hospitality, retail, promotions, and tutoring. Our intelligent matching system recommends suitable positions based on your skills and availability. With real-time salary settlement and secure work environment, find your ideal part-time job effortlessly and achieve perfect work-life balance.

## 用户细分

### 主要用户群体

1. **大学生群体 (40%)**
   - 年龄：18-25岁
   - 需求：课余时间赚取生活费，积累工作经验
   - 特点：时间灵活，学习能力强，偏好轻松有趣的工作

2. **职场新人 (25%)**
   - 年龄：22-28岁
   - 需求：增加收入来源，拓展职业技能
   - 特点：有一定工作经验，追求高薪和成长机会

3. **自由职业者 (20%)**
   - 年龄：25-35岁
   - 需求：寻找项目制工作，保持收入稳定
   - 特点：专业技能突出，时间自主性强

4. **宝妈群体 (15%)**
   - 年龄：25-40岁
   - 需求：在照顾家庭的同时获得收入
   - 特点：时间有限但相对固定，偏好居家或就近工作

## 页面层级结构

```
应用结构：
├─ L1: 首页
│  ├─ L2: 职位详情页
│  │  ├─ L3: 企业详情页
│  │  ├─ L3: 申请确认页
│  │  └─ L3: 收藏成功页
│  ├─ L2: 搜索结果页
│  │  ├─ L3: 筛选页面
│  │  └─ L3: 职位详情页
│  └─ L2: 分类浏览页
│     └─ L3: 职位列表页
├─ L1: 我的工作
│  ├─ L2: 进行中工作
│  │  ├─ L3: 工作详情页
│  │  ├─ L3: 签到页面
│  │  └─ L3: 工作评价页
│  ├─ L2: 已完成工作
│  │  ├─ L3: 工作详情页
│  │  └─ L3: 薪资详情页
│  ├─ L2: 申请记录
│  │  └─ L3: 申请详情页
│  └─ L2: 收藏夹
│     └─ L3: 职位详情页
└─ L1: 个人中心
   ├─ L2: 个人资料
   │  ├─ L3: 编辑资料页
   │  ├─ L3: 技能标签页
   │  └─ L3: 工作经历页

```

### 层级属性说明表

| 页面名称 | 层级 | 上级页面 | 下级页面 | 访问路径 |
|---------|-----|--------|---------|--------|
| 首页 | L1 | - | 职位详情页, 搜索结果页, 分类浏览页 | /home |
| 职位详情页 | L2 | 首页 | 企业详情页, 申请确认页, 收藏成功页 | /home/<USER>
| 企业详情页 | L3 | 职位详情页 | - | /home/<USER>/company |
| 申请确认页 | L3 | 职位详情页 | - | /home/<USER>/apply |
| 搜索结果页 | L2 | 首页 | 筛选页面, 职位详情页 | /home/<USER>
| 筛选页面 | L3 | 搜索结果页 | - | /home/<USER>/filter |
| 分类浏览页 | L2 | 首页 | 职位列表页 | /home/<USER>
| 我的工作 | L1 | - | 进行中工作, 已完成工作, 申请记录, 收藏夹 | /work |
| 进行中工作 | L2 | 我的工作 | 工作详情页, 签到页面, 工作评价页 | /work/ongoing |
| 签到页面 | L3 | 进行中工作 | - | /work/ongoing/checkin |
| 已完成工作 | L2 | 我的工作 | 工作详情页, 薪资详情页 | /work/completed |
| 申请记录 | L2 | 我的工作 | 申请详情页 | /work/applications |
| 收藏夹 | L2 | 我的工作 | 职位详情页 | /work/favorites |
| 个人中心 | L1 | - | 个人资料| /profile |
| 个人资料 | L2 | 个人中心 | 编辑资料页, 技能标签页, 工作经历页 | /profile/info |
| 编辑资料页 | L3 | 个人资料 | - | /profile/info/edit |

## 核心功能

### 1. 职位浏览与搜索
- **首页推荐**：基于用户画像智能推荐合适职位
- **分类浏览**：按行业、薪资、工作时间等维度分类
- **搜索功能**：支持关键词搜索，地理位置筛选
- **筛选排序**：多维度筛选（薪资、距离、工作时间、企业规模）

### 2. 职位申请与管理
- **一键申请**：简化申请流程，支持批量申请
- **申请状态跟踪**：实时查看申请进度
- **面试安排**：在线预约面试时间
- **工作确认**：双向确认工作安排

### 3. 工作执行与考勤
- **GPS签到**：基于地理位置的签到功能（待开发）
- **工作记录**：记录工作时长和内容
- **实时沟通**：与雇主在线沟通工作事宜
- **工作评价**：双向评价系统

### 4. 薪资结算
- **实时计算**：根据工作时长自动计算薪资
- **在线结算**：支持日结、周结等多种结算方式
- **提现功能**：快速提现到银行卡或支付宝（待开发）
- **收入统计**：详细的收入报表和统计

### 5. 用户管理
- **个人档案**：完善的个人信息和技能展示
- **信用体系**：基于工作表现的信用评级
- **消息通知**：及时推送工作相关信息
- **安全保障**：身份认证和资金安全保护

## 数据模型

```javascript
// 用户模型
User {
  id: String,
  phone: String,
  name: String,
  avatar: String,
  age: Number,
  gender: String,
  location: {
    city: String,
    district: String,
    coordinates: [Number, Number]
  },
  skills: [String],
  experience: [WorkExperience],
  creditScore: Number,
  isVerified: Boolean,
  createdAt: Date
}

// 职位模型
Job {
  id: String,
  title: String,
  description: String,
  company: Company,
  category: String,
  salary: {
    type: String, // hourly, daily, project
    amount: Number,
    currency: String
  },
  location: {
    address: String,
    coordinates: [Number, Number]
  },
  requirements: [String],
  workTime: {
    startDate: Date,
    endDate: Date,
    schedule: String
  },
  status: String, // active, filled, expired
  applicantCount: Number,
  createdAt: Date
}

// 申请记录模型
Application {
  id: String,
  userId: String,
  jobId: String,
  status: String, // pending, accepted, rejected, completed
  appliedAt: Date,
  interviewTime: Date,
  workRecord: WorkRecord,
  rating: Rating
}

// 工作记录模型
WorkRecord {
  id: String,
  applicationId: String,
  checkInTime: Date,
  checkOutTime: Date,
  workHours: Number,
  location: [Number, Number],
  notes: String,
  photos: [String],
  salary: Number,
  isPaid: Boolean
}

// 企业模型
Company {
  id: String,
  name: String,
  logo: String,
  description: String,
  industry: String,
  scale: String,
  rating: Number,
  location: String,
  isVerified: Boolean
}
```

---

*文档版本：v1.0*  
*创建日期：2025-08-16*  
*最后更新：2025-08-16*
